<template>
    <div class="layout-px-spacing">
        <breadcrumb :lists="breadcrumb_option"></breadcrumb>
        <div class="row layout-top-spacing">
            <div class="col-xl-12 col-lg-12 col-sm-12 layout-spacing">
                <b-overlay :show="html.overlay.show" rounded="top">
                    <div class="panel br-6 p-0">
                        <div class="custom-table">
                            <div class="p-15">
                                <b-card class="card-body">
                                    <b-form-row>
                                        <b-form-group
                                            label="Sở GDĐT"
                                            class="col mr-b-5"
                                        >
                                            <b-form-select
                                                v-model="filter.province"
                                                :options="resources.provinces"
                                                value-field="id"
                                                text-field="name"
                                                @input="changeProvince"
                                            ></b-form-select>
                                        </b-form-group>
                                        <b-form-group
                                            label="Phòng GDDT"
                                            class="col mr-b-5"
                                        >
                                            <b-form-select
                                                v-model="filter.district"
                                                :options="resources.districts"
                                                value-field="id"
                                                text-field="name"
                                            ></b-form-select>
                                        </b-form-group>

                                        <b-form-group
                                            label="Dự án"
                                            class="col mr-b-5"
                                        >
                                            <b-form-select
                                                v-model="filter.product_id"
                                                :options="resources.products"
                                                value-field="id"
                                                text-field="short_name"
                                                @input="change_province"
                                            ></b-form-select>
                                        </b-form-group>
                                        <b-form-group
                                            label="Trạng thái"
                                            class="col mr-b-5"
                                        >
                                            <b-form-select
                                                v-model="filter.status"
                                                :options="
                                                    Object.entries({
                                                        '': '- Tất cả -',
                                                        ...{
                                                            0: 'Mới',
                                                            1: 'Cấp cho đối tác',
                                                            2: 'Đã kích hoạt',
                                                            3: 'Hết hạn',
                                                            4: 'Tạm khóa',
                                                        },
                                                    }).map(([value, text]) => ({
                                                        value,
                                                        text,
                                                    }))
                                                "
                                            ></b-form-select>
                                        </b-form-group>
                                        <b-form-group
                                            label="Hết hạn sau"
                                            class="col mr-b-5"
                                        >
                                            <b-form-select
                                                v-model="filter.expired_in"
                                                :options="[
                                                    {
                                                        value: '',
                                                        text: '- Tất cả -',
                                                    },
                                                    {
                                                        value: 15,
                                                        text: 'Sau 15 ngày',
                                                    },
                                                    {
                                                        value: 30,
                                                        text: 'Sau 30 ngày',
                                                    },
                                                    {
                                                        value: 60,
                                                        text: 'Sau 2 tháng',
                                                    },
                                                    {
                                                        value: 90,
                                                        text: 'Sau 3 tháng',
                                                    },
                                                ]"
                                            />
                                        </b-form-group>
                                        <b-form-group
                                            label="Số tháng"
                                            class="col mr-b-5"
                                        >
                                            <b-form-select
                                                v-model="filter.month"
                                                :options="[
                                                    {
                                                        value: '',
                                                        text: '- Tất cả -',
                                                    },
                                                    {
                                                        value: 1,
                                                        text: '1 tháng',
                                                    },
                                                    {
                                                        value: 3,
                                                        text: '3 tháng',
                                                    },
                                                    {
                                                        value: 6,
                                                        text: '6 tháng',
                                                    },
                                                    {
                                                        value: 12,
                                                        text: '12 tháng',
                                                    },
                                                    {
                                                        value: 24,
                                                        text: '24 tháng',
                                                    },
                                                    {
                                                        value: 36,
                                                        text: '36 tháng',
                                                    },
                                                ]"
                                            />
                                        </b-form-group>
                                        <b-form-group
                                            label="Tên tài khoản"
                                            class="col mr-b-5"
                                        >
                                            <b-input
                                                type="text"
                                                v-model="filter.project_account"
                                            ></b-input>
                                        </b-form-group>
                                    </b-form-row>
                                    <div class="group-button">
                                        <b-button
                                            class="mr-05"
                                            variant="warning"
                                            @click="reset"
                                            ><i
                                                class="fa-solid fa-rotate-right"
                                            ></i>
                                            Làm mới
                                        </b-button>
                                        <b-button
                                            class="mr-05"
                                            variant="info"
                                            @click="change_filter"
                                            ><i
                                                class="fa-solid fa-magnifying-glass"
                                            ></i>
                                            Tìm kiếm
                                        </b-button>
                                    </div>
                                </b-card>
                            </div>
                            <b-table
                                ref="basic_table"
                                responsive
                                hover
                                :items="items"
                                :fields="columns"
                                :key="'license-table-' + items.length"
                            >
                                <template #cell(index)="data">
                                    {{ data.index + 1 }}
                                </template>
                                <template #cell(type)="data">
                                    {{ data.value == 0 ? "Tái ký" : "Ký mới" }}
                                </template>
                                <template #cell(status)="data">
                                    <span
                                        :class="getStatusColorClass(data.value)"
                                    >
                                        {{ getStatusLabel(data.value) }}
                                    </span>
                                </template>
                                <template #cell(link_recommend)="row">
                                    <a
                                        v-bind:href="row.item.link_recommend"
                                        target="_blank"
                                        style="color: #007bff !important"
                                    >
                                        {{ row.value.slice(0, 40) }}...
                                    </a>
                                </template>

                                <template #cell(action)="row">
                                    <div :key="'actions-' + row.item.id + '-' + row.item.status + '-' + (row.item.expired_at || '')">
                                        <!-- Nút khóa/mở khóa cho license còn hạn -->
                                        <a
                                            v-if="
                                                row.item.id &&
                                                isLicenseValid(row.item) &&
                                                row.item.status === 2
                                            "
                                            v-permission="['license_edit']"
                                            href="javascript:;"
                                            class="cancel"
                                            @click="lockLicense(row.item)"
                                            title="Khóa"
                                        >
                                            <i class="fa-solid fa-lock"></i>
                                        </a>
                                        <a
                                            v-if="
                                                row.item.id &&
                                                isLicenseValid(row.item) &&
                                                row.item.status === 4
                                            "
                                            v-permission="['license_edit']"
                                            href="javascript:;"
                                            class="success"
                                            @click="unlockLicense(row.item)"
                                            title="Mở khóa"
                                        >
                                            <i class="fa-solid fa-unlock"></i>
                                        </a>

                                        <!-- Nút gia hạn cho license hết hạn -->
                                        <a
                                            v-if="
                                                row.item.id &&
                                                isLicenseExpired(row.item) &&
                                                canRenewLicense(row.item)
                                            "
                                            v-permission="['license_edit']"
                                            href="javascript:;"
                                            class="primary"
                                            @click="renewLicense(row.item)"
                                            title="Gia hạn"
                                        >
                                            <i class="fa-solid fa-refresh"></i>
                                        </a>

                                        <!-- Thông báo không thể gia hạn -->
                                        <span
                                            v-if="
                                                row.item.id &&
                                                isLicenseExpired(row.item) &&
                                                !canRenewLicense(row.item)
                                            "
                                            class="text-warning"
                                            title="Đơn vị này đã có giấy phép đang hoạt động"
                                        >
                                            <i
                                                class="fa-solid fa-exclamation-triangle"
                                            ></i>
                                        </span>

                                        <!-- Nút chỉnh sửa - chỉ hiển thị cho license còn hạn -->
                                        <a
                                            v-if="row.item.id && isLicenseValid(row.item)"
                                            v-permission="['license_edit']"
                                            href="javascript:;"
                                            class="edit ml-2"
                                            @click="show_modal(row.item, 'edit')"
                                            title="Chỉnh sửa"
                                        >
                                            <i class="fa-solid fa-pen"></i>
                                        </a>

                                        <!-- Nút xem chi tiết -->
                                        <a
                                            class="ml-2"
                                            title="Xem chi tiết"
                                            href="javascript:;"
                                            @click="show_modal(row.item, 'view')"
                                        >
                                            <i class="fa-solid fa-eye"></i>
                                        </a>
                                    </div>
                                </template>
                            </b-table>
                            <pagination
                                :table_option="table_option"
                                :routing="change_page"
                            ></pagination>
                        </div>
                    </div>
                    <template #overlay>
                        <div class="text-center">
                            <div
                                class="d-flex justify-content-between mx-5 mt-3 mb-3"
                            >
                                <div
                                    class="spinner-border text-success align-self-center loader-lg"
                                >
                                    Loading...
                                </div>
                            </div>
                            <h3 id="cancel-label">Đang xử lý...</h3>
                        </div>
                    </template>
                </b-overlay>
            </div>
        </div>

        <b-modal
            id="LicenseDetailModalCenter"
            :title="
                modal_mode === 'edit'
                    ? 'Chỉnh sửa giấy phép'
                    : 'Thông tin giấy phép'
            "
            centered
            v-model="show_detail_modal"
            size="lg"
            @ok="handleSubmit"
            :ok-title="modal_mode === 'edit' ? 'Lưu' : 'Đóng'"
            :cancel-title="modal_mode === 'edit' ? 'Hủy' : null"
            :hide-footer="modal_mode === 'view'"
        >
            <!-- Cảnh báo cho license hết hạn -->
            <div
                v-if="isLicenseExpired(selected_item)"
                class="alert alert-warning mb-3"
            >
                <i class="fa-solid fa-exclamation-triangle"></i>
                <strong>Giấy phép đã hết hạn!</strong> Không thể chỉnh sửa hoặc
                thực hiện các thao tác khóa/mở khóa.
            </div>
            <b-tabs>
                <b-tab title="Thông tin" active>
                    <div class="modal-body">
                        <div class="row">
                            <template v-if="modal_mode === 'edit'">
                                <div class="col-md-6">
                                    <b-form-group label="Mã giấy phép:">
                                        <div class="form-control-plaintext">
                                            {{ edit_item.code }}
                                        </div>
                                    </b-form-group>
                                    <b-form-group label="Dự án:">
                                        <div class="form-control-plaintext">
                                            {{
                                                getProjectName(
                                                    edit_item.project_code
                                                )
                                            }}
                                        </div>
                                    </b-form-group>
                                    <b-form-group label="Số tháng:">
                                        <div class="form-control-plaintext">
                                            {{ edit_item.month }}
                                        </div>
                                    </b-form-group>
                                    <b-form-group label="Trạng thái:">
                                        <div class="form-control-plaintext">
                                            {{
                                                getStatusLabel(edit_item.status)
                                            }}
                                        </div>
                                    </b-form-group>
                                    <b-form-group
                                        label="Chú ý:"
                                        class="text-danger"
                                    >
                                        <p class="mb-0 text-danger">
                                            - Sau khi kích hoạt 3 ngày, hệ thống
                                            không cho phép sửa ngày kích
                                            hoạt.<br />
                                            - Ngày kích hoạt mới không được cách
                                            ngày kích hoạt cũ quá 15 ngày.<br />
                                            - Ngày kích hoạt phải sau hoặc bằng
                                            ngày hiện tại.<br />
                                            - Chỉ được phép chỉnh sửa tối đa 5
                                            lần.
                                        </p>
                                    </b-form-group>
                                </div>
                                <div class="col-md-6">
                                    <b-form-group label="Sở GDĐT:">
                                        <div class="form-control-plaintext">
                                            {{ edit_item.province_name }}
                                        </div>
                                    </b-form-group>
                                    <b-form-group label="Phòng GDĐT:">
                                        <div class="form-control-plaintext">
                                            {{ edit_item.district_name }}
                                        </div>
                                    </b-form-group>
                                    <b-form-group label="Tên trường:">
                                        <div class="form-control-plaintext">
                                            {{ edit_item.project_unit_name }}
                                        </div>
                                    </b-form-group>
                                    <b-form-group label="Tài khoản:">
                                        <b-form-input
                                            v-model="edit_item.project_account"
                                        ></b-form-input>
                                    </b-form-group>
                                    <b-form-group label="Ngày bắt đầu:">
                                        <flat-pickr
                                            v-model="edit_item.activated_at"
                                            :config="flatPickrConfig"
                                            class="form-control flatpickr"
                                            placeholder="Chọn ngày bắt đầu"
                                            @on-change="onStartDateChange"
                                        ></flat-pickr>
                                    </b-form-group>
                                    <b-form-group label="Ngày kết thúc:">
                                        <div class="form-control-plaintext">
                                            {{ edit_item.expired_at }}
                                        </div>
                                    </b-form-group>
                                </div>
                            </template>
                            <template v-else>
                                <div class="col-md-6">
                                    <p>
                                        <b>Mã giấy phép:</b>
                                        {{ selected_item.code }}
                                    </p>
                                    <p>
                                        <b>Dự án:</b>
                                        {{
                                            getProjectName(
                                                selected_item.project_code
                                            )
                                        }}
                                    </p>
                                    <p>
                                        <b>Số tháng:</b>
                                        {{ selected_item.month }}
                                    </p>
                                    <p>
                                        <b>Ngày cấp:</b>
                                        {{ selected_item.assigned_at }}
                                    </p>
                                    <p>
                                        <b>Ngày kích hoạt:</b>
                                        {{ selected_item.activated_at }}
                                    </p>
                                    <p>
                                        <b>Ngày hết hạn:</b>
                                        {{ selected_item.expired_at }}
                                    </p>
                                    <p>
                                        <b>Trạng thái:</b>
                                        <span
                                            :class="
                                                getStatusColorClass(
                                                    selected_item.status
                                                )
                                            "
                                        >
                                            {{
                                                getStatusLabel(
                                                    selected_item.status
                                                )
                                            }}
                                        </span>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p>
                                        <b>Sở GDĐT:</b>
                                        {{ selected_item.province_name }}
                                    </p>
                                    <p>
                                        <b>Phòng GDĐT:</b>
                                        {{ selected_item.district_name }}
                                    </p>
                                    <p>
                                        <b>Trường:</b>
                                        {{ selected_item.project_unit_name }}
                                    </p>
                                    <p>
                                        <b>Tài khoản:</b>
                                        {{ selected_item.project_account }}
                                    </p>
                                </div>
                            </template>
                        </div>
                    </div>
                </b-tab>
                <b-tab
                    v-if="
                        selected_item.activities &&
                        selected_item.activities.length
                    "
                    title="Lịch sử thay đổi"
                >
                    <div class="modal-body">
                        <b-table
                            :items="selected_item.activities"
                            :fields="activityFields"
                            small
                            responsive
                        >
                            <template #cell(created_at)="row">
                                {{ row.item.created_at | formatDateTime }}
                            </template>
                            <template #cell(causer)="row">
                                {{ row.item.causer }}
                            </template>
                            <template #cell(changes)="row">
                                <ul class="mb-0">
                                    <li
                                        v-for="(change, key) in row.item
                                            .changes"
                                        :key="key"
                                    >
                                        <b>{{ key }}:</b><br />
                                        <span class="text-danger">{{
                                            change.old
                                        }}</span>
                                        <i class="fa fa-arrow-right mx-1"></i>
                                        <span class="text-success">{{
                                            change.new
                                        }}</span>
                                    </li>
                                </ul>
                            </template>
                        </b-table>
                    </div>
                </b-tab>
            </b-tabs>
        </b-modal>

        <!-- Modal gia hạn license -->
        <b-modal
            id="RenewLicenseModal"
            title="Gia hạn giấy phép"
            centered
            v-model="show_renew_modal"
            @ok="handleRenewSubmit"
            ok-title="Gia hạn"
            cancel-title="Hủy"
            ok-variant="primary"
        >
            <div class="modal-body">
                <div v-if="renew_item.code" class="mb-3">
                    <p><strong>Mã giấy phép:</strong> {{ renew_item.code }}</p>
                    <p>
                        <strong>Dự án:</strong>
                        {{ getProjectName(renew_item.project_code) }}
                    </p>
                    <p>
                        <strong>Tài khoản:</strong>
                        {{ renew_item.project_account }}
                    </p>
                    <p>
                        <strong>Trường:</strong>
                        {{ renew_item.project_unit_name }}
                    </p>
                </div>

                <b-form-group
                    label="Chọn thời hạn gia hạn:"
                    label-for="renewal-month"
                >
                    <b-form-select
                        id="renewal-month"
                        v-model="selected_month"
                        :options="[
                            { value: '', text: '- Chọn thời hạn -' },
                            { value: 1, text: '1 tháng' },
                            { value: 3, text: '3 tháng' },
                            { value: 6, text: '6 tháng' },
                            { value: 12, text: '12 tháng' },
                            { value: 24, text: '24 tháng' },
                            { value: 36, text: '36 tháng' },
                        ]"
                        required
                        @input="calculateEndDate"
                    ></b-form-select>
                </b-form-group>

                <b-form-group
                    label="Chọn ngày bắt đầu:"
                    label-for="renewal-start-date"
                >
                    <flat-pickr
                        id="renewal-start-date"
                        v-model="selected_start_date"
                        :config="renewalDateConfig"
                        class="form-control flatpickr"
                        placeholder="Chọn ngày bắt đầu"
                        @on-change="calculateEndDate"
                    ></flat-pickr>
                </b-form-group>

                <!-- Hiển thị thông tin ngày bắt đầu và kết thúc -->
                <div
                    v-if="selected_month && selected_start_date"
                    class="alert alert-success"
                >
                    <h6>
                        <i class="fa-solid fa-calendar-check"></i> Thông tin gia
                        hạn:
                    </h6>
                    <div class="row">
                        <div class="col-6">
                            <strong>Ngày bắt đầu:</strong><br />
                            {{ formatDate(selected_start_date) }}
                        </div>
                        <div class="col-6">
                            <strong>Ngày kết thúc:</strong><br />
                            {{ calculated_end_date }}
                        </div>
                    </div>
                    <div class="mt-2">
                        <strong>Thời hạn:</strong> {{ selected_month }} tháng
                    </div>
                </div>

                <div class="alert alert-info">
                    <i class="fa-solid fa-info-circle"></i>
                    Hệ thống sẽ tự động tìm và gán một giấy phép mới với thời
                    hạn đã chọn.
                </div>
            </div>
        </b-modal>
    </div>
</template>

<script>
import "@/assets/sass/scrollspyNav.scss";
import "@/assets/sass/users/user-profile.scss";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import "@/assets/sass/forms/custom-flatpickr.css";
import c from "@/helpers/common";
import permission from "@/directive/permission/index.js";
import breadcrumb from "@/views/components/breadcrumb";
import pagination from "@/views/components/pagination";
import storage from "@/helpers/storage";
import { fetchInfoData } from "@/helpers/partner_auth";
import checkPermission from "@/helpers/permission";

export default {
    components: {
        breadcrumb,
        pagination,
        flatPickr,
    },
    directives: { permission },
    data() {
        return {
            breadcrumb_option: ["Đối tác", "Giấy phép cấp cho đối tác"],
            show_detail_modal: false,
            show_renew_modal: false,
            selected_item: {},
            edit_item: {},
            renew_item: {},
            selected_month: "",
            selected_start_date: "",
            calculated_end_date: "",
            modal_mode: "view", // 'view', 'edit', or 'lock'
            flatPickrConfig: {
                enableTime: false,
                dateFormat: "Y-m-d",
                locale: "vn",
                placeholder: "Chọn ngày",
                timezone: "Asia/Ho_Chi_Minh", // UTC+7 timezone
            },
            renewalDateConfig: {
                enableTime: false,
                dateFormat: "Y-m-d",
                locale: "vn",
                placeholder: "Chọn ngày bắt đầu",
                minDate: "today",
                timezone: "Asia/Ho_Chi_Minh",
            },
            key: "qlmn",
            columns: [],
            items: [],
            table_option: {
                total: "",
                current_page: 1,
                per_page: "",
                from: "",
                to: "",
            },
            html: {
                overlay: {
                    show: false,
                },
                modal: {
                    show: false,
                    title: "",
                },
            },
            filter: {
                product_id: "",
                status: "",
                expired_in: "",
                project_account: "",
                month: "",
                province: "",
                district: "",
            },
            resources: {
                provinces: [{ id: "", name: "- Chọn -" }],
                districts: [{ id: "", name: "- Chọn -" }],
                school_levels: [{ level: "", name: "Chọn Cấp học" }],
                products: [{ id: "", short_name: "- Chọn -" }],
            },
            info: [],
            activityFields: [
                {
                    key: "created_at",
                    label: "Thời gian",
                    thClass: "text-center",
                    tdClass: "text-center",
                },
                {
                    key: "causer",
                    label: "Người thực hiện",
                    thClass: "text-center",
                    tdClass: "text-center",
                },
                { key: "changes", label: "Nội dung thay đổi" },
            ],
        };
    },
    computed: {
        // Computed properties for better reactivity
        licenseStatusMap() {
            const map = new Map();
            this.items.forEach(item => {
                if (item.expired_at) {
                    const expiredDate = new Date(item.expired_at);
                    const now = new Date();
                    map.set(item.id, {
                        isValid: expiredDate > now,
                        isExpired: expiredDate <= now,
                        canRenew: this.checkCanRenewLicense(item)
                    });
                } else {
                    map.set(item.id, {
                        isValid: false,
                        isExpired: false,
                        canRenew: false
                    });
                }
            });
            return map;
        }
    },
    watch: {
        // Watch for changes in items to ensure reactivity
        items: {
            handler() {
                // Force update of computed properties when items change
                this.$nextTick(() => {
                    this.$forceUpdate();
                });
            },
            deep: true
        }
    },
    async created() {
        this.$store.commit("toggleLoading", true);

        try {
            const { products } = c.partner_session().categories;
            this.resources.products = this.resources.products.concat(products);

            // Lấy thông tin info từ IndexedDB
            let infoData = await storage.getObject(`info_${this.key}`);

            if (!infoData) {
                // Lấy thông tin info từ API lưu vào IndexedDB
                infoData = await fetchInfoData(this.key);
            }
            this.info = infoData;

            if (this.info && this.info.data && this.info.data.units) {
                this.resources.provinces = [
                    ...this.resources.provinces,
                    ...this.info.data.units,
                ];
            } else {
                console.log("No units data found in info");
            }

            if (infoData) {
                
                await this.get_data(this.table_option.current_page);
                this.bind_data();
                // Force a re-render to ensure all computed properties are updated
                await this.$nextTick();
            }
        } catch (error) {
            console.error('Error during component initialization:', error);
        } finally {
            this.$store.commit("toggleLoading", false);
        }
    },
    methods: {
        bind_data() {
            this.columns = [
                {
                    key: "index",
                    label: "STT",
                    thClass: "text-center",
                    tdClass: "text-center",
                },
                {
                    key: "code",
                    label: "Mã giấy phép",
                    thClass: "text-left",
                    formatter: (value) => {
                        return value.length > 10
                            ? value.slice(0, 10) + "..."
                            : value;
                    },
                },
                {
                    key: "project_code",
                    label: "Dự án",
                    thClass: "text-left",
                    formatter: (value) => {
                        const product = this.resources.products.find(
                            (p) => p.id == value
                        );
                        return product ? product.code : value;
                    },
                },
                {
                    key: "month",
                    label: "Tháng",
                    thClass: "text-left",
                },
                {
                    key: "project_account",
                    label: "Cấp cho tài khoản",
                    thClass: "text-left",
                },
                {
                    key: "project_unit_name",
                    label: "Đơn vị",
                    thClass: "text-left",
                },
                {
                    key: "assigned_at",
                    label: "Ngày cấp",
                    thClass: "text-left",
                },
                {
                    key: "activated_at_vn",
                    label: "Ngày kích hoạt",
                    thClass: "text-left",
                },
                {
                    key: "expired_at_vn",
                    label: "Ngày hết hạn",
                    thClass: "text-left",
                },
                {
                    key: "status",
                    label: "Trạng thái",
                    thClass: "text-left",
                },
                {
                    key: "action",
                    label: "",
                    // thClass: "th",
                    class: "actions text-right",
                },
            ];
        },
        getStatusLabel(status) {
            const STATUS_LABELS = {
                0: "Mới",
                1: "Cấp cho đối tác",
                2: "Đã kích hoạt",
                3: "Hết hạn",
                4: "Tạm khóa",
            };
            return STATUS_LABELS[status] || "Unknown";
        },
        getStatusColorClass(status) {
            const STATUS_COLORS = {
                0: "badge badge-info",
                1: "badge badge-primary",
                2: "badge badge-success",
                3: "badge badge-danger",
                4: "badge badge-warning",
            };
            return STATUS_COLORS[status] || "badge badge-secondary";
        },
        async get_data(page) {
            this.html.overlay.show = true;
            try {
                const response = await c.g(`/api/partners/license/list?page=${page}`, this.filter);
                this.table_option.current_page = response.current_page;
                this.table_option.per_page = response.pagination.per_page;
                this.table_option.from = response.pagination.from;
                this.table_option.to = response.pagination.to;
                this.table_option.total = response.pagination.total;
                this.items = response.data;

                // Force Vue to update the DOM after data changes
                await this.$nextTick();
            } catch (error) {
                console.error('Error loading license data:', error);
            } finally {
                this.html.overlay.show = false;
            }
        },
        async change_page(page) {
            await this.get_data(page);
        },
        async change_filter() {
            await this.get_data();
        },
        // Helper methods to check license status - now using computed properties for better reactivity
        isLicenseValid(item) {
            if (!item || !item.id) return false;
            const status = this.licenseStatusMap.get(item.id);
            return status ? status.isValid : false;
        },
        isLicenseExpired(item) {
            if (!item || !item.id) return false;
            const status = this.licenseStatusMap.get(item.id);
            return status ? status.isExpired : false;
        },
        canRenewLicense(item) {
            if (!item || !item.id) return false;
            const status = this.licenseStatusMap.get(item.id);
            return status ? status.canRenew : false;
        },
        // Separate method for checking renewal capability (used in computed)
        checkCanRenewLicense(item) {
            if (!item || !this.items || this.items.length === 0) return false;
            // Kiểm tra xem có license nào khác với cùng project_unit_ref đang ở trạng thái active (status = 2) không
            const activeLicense = this.items.find(
                (license) =>
                    license.project_unit_ref === item.project_unit_ref &&
                    license.status === 2 &&
                    license.id !== item.id
            );
            return !activeLicense;
        },

        // License management methods
        async lockLicense(item) {
            try {
                const result = await this.$swal({
                    title: "Xác nhận khóa giấy phép",
                    text: `Bạn có chắc chắn muốn khóa giấy phép "${item.code}"?`,
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonText: "Khóa",
                    cancelButtonText: "Hủy",
                    confirmButtonColor: "#e7515a",
                    cancelButtonColor: "#6c757d",
                });

                if (result.isConfirmed) {
                    this.html.overlay.show = true;
                    await c.p(`/api/partners/license/lock/${item.id}`);

                    this.$swal({
                        icon: "success",
                        title: "Thành công",
                        text: "Khóa giấy phép thành công!",
                        confirmButtonText: "OK",
                    });

                    await this.get_data(this.table_option.current_page);
                }
            } catch (error) {
                console.error("Error locking license:", error);
                this.$swal({
                    icon: "error",
                    title: "Lỗi",
                    text:
                        error.response?.data?.message ||
                        "Có lỗi xảy ra khi khóa giấy phép!",
                    confirmButtonText: "OK",
                });
            } finally {
                this.html.overlay.show = false;
            }
        },

        async unlockLicense(item) {
            try {
                const result = await this.$swal({
                    title: "Xác nhận mở khóa giấy phép",
                    text: `Bạn có chắc chắn muốn mở khóa giấy phép "${item.code}"?`,
                    icon: "question",
                    showCancelButton: true,
                    confirmButtonText: "Mở khóa",
                    cancelButtonText: "Hủy",
                    confirmButtonColor: "#1abc9c",
                    cancelButtonColor: "#6c757d",
                });

                if (result.isConfirmed) {
                    this.html.overlay.show = true;
                    await c.p(`/api/partners/license/unlock/${item.id}`);

                    this.$swal({
                        icon: "success",
                        title: "Thành công",
                        text: "Mở khóa giấy phép thành công!",
                        confirmButtonText: "OK",
                    });

                    await this.get_data(this.table_option.current_page);
                }
            } catch (error) {
                console.error("Error unlocking license:", error);
                this.$swal({
                    icon: "error",
                    title: "Lỗi",
                    text:
                        error.response?.data?.message ||
                        "Có lỗi xảy ra khi mở khóa giấy phép!",
                    confirmButtonText: "OK",
                });
            } finally {
                this.html.overlay.show = false;
            }
        },

        renewLicense(item) {
            // Kiểm tra xem có thể gia hạn không
            if (!this.canRenewLicense(item)) {
                this.$swal({
                    icon: "warning",
                    title: "Không thể gia hạn",
                    text: "Đơn vị này đã có giấy phép đang hoạt động. Không thể gia hạn thêm!",
                    confirmButtonText: "OK",
                });
                return;
            }

            this.renew_item = { ...item };
            this.selected_month = "";
            this.selected_start_date = "";
            this.calculated_end_date = "";
            this.show_renew_modal = true;
        },

        // Tính toán ngày kết thúc dựa trên ngày bắt đầu và số tháng
        calculateEndDate() {
            if (this.selected_month && this.selected_start_date) {
                const startDate = new Date(this.selected_start_date);
                const endDate = new Date(startDate);
                endDate.setMonth(
                    endDate.getMonth() + parseInt(this.selected_month)
                );
                endDate.setDate(endDate.getDate() - 1); // Trừ 1 ngày để ngày kết thúc là ngày cuối cùng của chu kỳ

                this.calculated_end_date = this.formatDate(
                    endDate.toISOString().split("T")[0]
                );
            } else {
                this.calculated_end_date = "";
            }
        },

        // Format date to Vietnamese format
        formatDate(dateString) {
            if (!dateString) return "";
            const date = new Date(dateString);
            return date.toLocaleDateString("vi-VN");
        },

        async handleRenewSubmit(evt) {
            evt.preventDefault();

            if (!this.selected_month) {
                this.$swal({
                    icon: "warning",
                    title: "Thiếu thông tin",
                    text: "Vui lòng chọn thời hạn gia hạn!",
                    confirmButtonText: "OK",
                });
                return;
            }

            if (!this.selected_start_date) {
                this.$swal({
                    icon: "warning",
                    title: "Thiếu thông tin",
                    text: "Vui lòng chọn ngày bắt đầu!",
                    confirmButtonText: "OK",
                });
                return;
            }

            try {
                this.html.overlay.show = true;
                const response = await c.p(
                    `/api/partners/license/renew/${this.renew_item.id}`,
                    {
                        month: this.selected_month,
                        start_date: this.selected_start_date,
                    }
                );

                console.log(response);

                if (response.license) {
                    this.$swal({
                        icon: "success",
                        title: "Thành công",
                        html: `
                        <div class="text-left">
                            <p><strong>Gia hạn giấy phép thành công!</strong></p>
                            <p><strong>Thời hạn:</strong> ${this.selected_month} tháng</p>
                            <p><strong>Ngày bắt đầu:</strong> ${response.start_date}</p>
                            <p><strong>Ngày kết thúc:</strong> ${response.end_date}</p>
                        </div>
                    `,
                        confirmButtonText: "OK",
                    });

                    this.show_renew_modal = false;
                    await this.get_data(this.table_option.current_page);
                }
            } catch (error) {
                console.error("Error renewing license:", error);
                let errorMessage = "Có lỗi xảy ra khi gia hạn giấy phép!";

                if (error.response?.data?.message) {
                    errorMessage = error.response.data.message;
                }

                this.$swal({
                    icon: "error",
                    title: "Lỗi gia hạn",
                    text: errorMessage,
                    confirmButtonText: "OK",
                });
            } finally {
                this.html.overlay.show = false;
            }
        },

        getProjectName(project_code) {
            const product = this.resources.products.find(
                (p) => p.id == project_code
            );
            return product ? product.short_name : project_code;
        },
        async reset() {
            this.filter = {
                product_id: "",
                status: "",
                expired_in: "",
                project_account: "",
                month: "",
                province: "",
                district: "",
            };
            await this.get_data();
        },

        async changeProvince() {
            this.$store.commit("toggleLoading", true);

            this.resources.districts = [{ id: "", name: "- Chọn -" }];

            try {
                // Get the selected province ID
                const provinceId = this.filter.province;

                if (provinceId) {
                    const selectedProvince = this.resources.provinces.find(
                        (province) => province.id === provinceId
                    );

                    if (selectedProvince) {
                        if (
                            selectedProvince.districts &&
                            Array.isArray(selectedProvince.districts)
                        ) {
                            this.resources.districts = [
                                { id: "", name: "- Chọn -" },
                                ...selectedProvince.districts,
                            ];
                        }
                    }
                }
            } catch (error) {
                console.error("Error setting districts:", error);
                this.$store.commit("setSnackBar", {
                    msg: "Có lỗi xảy ra khi tải dữ liệu quận/huyện!",
                    type: "error",
                });
            } finally {
                this.$store.commit("toggleLoading", false);
            }
        },
        show_modal(item, mode = "view") {
            this.selected_item = { ...item };

            // Kiểm tra nếu license hết hạn thì không cho phép edit
            if (mode === "edit" && this.isLicenseExpired(item)) {
                this.$swal({
                    icon: "warning",
                    title: "Không thể chỉnh sửa",
                    text: "Không thể chỉnh sửa giấy phép đã hết hạn!",
                    confirmButtonText: "OK",
                });
                return;
            }

            this.modal_mode = mode;
            if (mode === "edit") {
                this.edit_item = {
                    ...item,
                    province_id: item.province_id || "",
                    district_id: item.district_id || "",
                    activated_at: item.activated_at, // luôn lấy giá trị gốc
                };

                console.log(this.edit_item);
                // Tính lại ngày kết thúc khi mở form (phòng trường hợp dữ liệu cũ sai)
                this.onStartDateChange([item.activated_at]);
            }
            this.show_detail_modal = true;
        },
        async handleSubmit(evt) {
            evt.preventDefault();
            if (this.modal_mode !== "edit") {
                this.show_detail_modal = false;
                return;
            }

            try {
                this.html.overlay.show = true;
                const updateData = {
                    id: this.edit_item.id,
                    code: this.edit_item.code,
                    project_account: this.edit_item.project_account,
                    activated_at: this.edit_item.activated_at,
                    expired_at: this.edit_item.expired_at,
                };

                console.log(updateData);

                const response = await c.p(
                    "/api/partners/license/update/" + this.edit_item.id,
                    updateData
                );

                console.log(response);

                if (
                    response.license.after !== null ||
                    response.license.after !== undefined
                ) {
                    this.$swal({
                        icon: "success",
                        html: `Cập nhật giấy phép thành công`,
                        confirmButtonText: "Chấp nhận",
                        showLoaderOnConfirm: false,
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                    });
                    await this.get_data(this.table_option.current_page);
                    this.show_detail_modal = false;
                } else {
                    this.$swal({
                        icon: "error",
                        html: `Cập nhật giấy phép bị lỗi`,
                        confirmButtonText: "Chấp nhận",
                        showLoaderOnConfirm: false,
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                    });
                }
            } catch (error) {
                console.error("Error updating license:", error);
                this.$store.commit("setSnackBar", {
                    msg: "Có lỗi xảy ra khi cập nhật giấy phép!",
                    type: "error",
                });
            } finally {
                this.html.overlay.show = false;
            }
        },
        async changeProvinceEdit() {
            try {
                const provinceId = this.edit_item.province_id;
                this.edit_item.district_id = "";

                if (provinceId) {
                    const selectedProvince = this.resources.provinces.find(
                        (province) => province.id === provinceId
                    );

                    if (selectedProvince && selectedProvince.districts) {
                        this.resources.districts = [
                            { id: "", name: "- Chọn -" },
                            ...selectedProvince.districts,
                        ];
                    }
                } else {
                    this.resources.districts = [{ id: "", name: "- Chọn -" }];
                }
            } catch (error) {
                console.error("Error changing province in edit mode:", error);
                this.$store.commit("setSnackBar", {
                    msg: "Có lỗi xảy ra khi tải dữ liệu quận/huyện!",
                    type: "error",
                });
            }
        },
        onStartDateChange(selectedDates) {
            if (!selectedDates || !selectedDates[0]) return;
            const startDate = new Date(selectedDates[0]);
            const months = parseInt(this.edit_item.month) || 0;
            if (months > 0) {
                // Tính ngày kết thúc = ngày bắt đầu + số tháng
                const endDate = new Date(startDate);
                endDate.setMonth(endDate.getMonth() + months);
                // Trừ đi 1 ngày để ngày hết hạn là ngày cuối cùng của chu kỳ
                endDate.setDate(endDate.getDate());
                // Format lại yyyy-mm-dd
                const yyyy = endDate.getFullYear();
                const mm = String(endDate.getMonth() + 1).padStart(2, "0");
                const dd = String(endDate.getDate()).padStart(2, "0");
                this.edit_item.expired_at = `${yyyy}-${mm}-${dd}`;
            } else {
                this.edit_item.expired_at = "";
            }
        },
    },
    filters: {
        formatDateTime(value) {
            if (!value) return "";
            const d = new Date(value.replace(" ", "T"));
            return d.toLocaleString("vi-VN", { hour12: false });
        },
    },
};
</script>

<style scoped>
.badge {
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 12px;
    display: inline-block;
    min-width: 80px;
    text-align: center;
}
.badge-info {
    background-color: #2196f3;
    color: #fff;
}
.badge-primary {
    background-color: #4361ee;
    color: #fff;
}
.badge-success {
    background-color: #1abc9c;
    color: #fff;
}
.badge-danger {
    background-color: #e7515a;
    color: #fff;
}
.badge-warning {
    background-color: #e2a03f;
    color: #fff;
}
.badge-secondary {
    background-color: #805dca;
    color: #fff;
}

/* Action button styles */
.actions a {
    display: inline-block;
    padding: 0.375rem 0.5rem;
    margin: 0 0.125rem;
    border-radius: 0.25rem;
    text-decoration: none;
    transition: all 0.2s ease;
}

.actions a.cancel {
    color: #e7515a;
    background-color: rgba(231, 81, 90, 0.1);
}

.actions a.cancel:hover {
    background-color: #e7515a;
    color: #fff;
}

.actions a.success {
    color: #1abc9c;
    background-color: rgba(26, 188, 156, 0.1);
}

.actions a.success:hover {
    background-color: #1abc9c;
    color: #fff;
}

.actions a.primary {
    color: #4361ee;
    background-color: rgba(67, 97, 238, 0.1);
}

.actions a.primary:hover {
    background-color: #4361ee;
    color: #fff;
}

.actions a.edit {
    color: #e2a03f;
    background-color: rgba(226, 160, 63, 0.1);
}

.actions a.edit:hover {
    background-color: #e2a03f;
    color: #fff;
}

.actions a:last-child {
    color: #506690;
    background-color: rgba(80, 102, 144, 0.1);
}

.actions a:last-child:hover {
    background-color: #506690;
    color: #fff;
}

/* Custom Tab Styles */
:deep(.nav-tabs) {
    border-bottom: 2px solid #e0e6ed;
    margin-bottom: 1rem;
    background: #f8f9fa;
    padding: 0.5rem 0.5rem 0;
    border-radius: 8px 8px 0 0;
}

:deep(.nav-tabs .nav-item) {
    margin: 0 0.25rem;
}

:deep(.nav-tabs .nav-link) {
    border: 1px solid transparent;
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    color: #506690;
    position: relative;
    background: transparent;
    transition: all 0.2s ease;
}

:deep(.nav-tabs .nav-link:hover) {
    border-color: #e0e6ed;
    background: #ffffff;
    color: #4361ee;
}

:deep(.nav-tabs .nav-link.active) {
    color: #4361ee;
    background: #ffffff;
    border: 1px solid #e0e6ed;
    border-bottom: 2px solid #ffffff;
    margin-bottom: -2px;
}

:deep(.tab-content) {
    background: #ffffff;
    border: 1px solid #e0e6ed;
    border-top: none;
    border-radius: 0 0 8px 8px;
    padding: 1.5rem;
}

:deep(.tab-content .modal-body) {
    padding: 0;
}

/* Custom Datepicker Styles */
.custom-datepicker :deep(.b-form-datepicker) {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.custom-datepicker :deep(.form-control) {
    height: calc(1.5em + 1rem + 2px);
    padding: 0.5rem 1rem;
    font-size: 1rem;
    border-radius: 0.3rem;
    border: 1px solid #ced4da;
    background-color: #fff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.custom-datepicker :deep(.form-control:focus) {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.custom-datepicker :deep(.form-control:hover) {
    border-color: #4361ee;
}

.custom-datepicker :deep(.b-calendar) {
    border: none;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
        "Helvetica Neue", Arial, sans-serif;
}

.custom-datepicker :deep(.b-calendar-grid) {
    border-radius: 0 0 8px 8px;
}

.custom-datepicker :deep(.b-calendar-grid-body .col[data-date]) {
    padding: 0.65rem;
    transition: all 0.2s ease;
}

.custom-datepicker :deep(.b-calendar-grid-body .col[data-date]:hover) {
    background-color: #f8f9fa;
    border-radius: 50%;
}

.custom-datepicker :deep(.b-calendar-grid-body .selected) {
    background-color: #4361ee !important;
    border-radius: 50%;
    color: white !important;
}

.custom-datepicker :deep(.b-calendar-grid-body .today) {
    border-color: #4361ee !important;
}

.custom-datepicker :deep(.b-calendar-grid-body .weekend) {
    color: #e7515a;
}

.custom-datepicker :deep(.b-calendar-header) {
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0.5rem;
}

.custom-datepicker :deep(.b-calendar-header output) {
    font-weight: 600;
    color: #4361ee;
}

.custom-datepicker :deep(.btn-outline-primary) {
    color: #4361ee;
    border-color: #4361ee;
}

.custom-datepicker :deep(.btn-outline-primary:hover) {
    background-color: #4361ee;
    color: white;
}

.custom-datepicker :deep(.btn-outline-secondary) {
    color: #6c757d;
    border-color: #6c757d;
}

.custom-datepicker :deep(.btn-outline-secondary:hover) {
    background-color: #6c757d;
    color: white;
}

/* Custom Flatpickr styles */
:deep(.flatpickr) {
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.3rem;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    height: calc(1.5em + 1rem + 2px);
    width: 100%;
    cursor: pointer;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

:deep(.flatpickr:focus) {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
    outline: none;
}

:deep(.flatpickr-calendar) {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
        "Helvetica Neue", Arial, sans-serif;
}

:deep(.flatpickr-day) {
    border-radius: 50%;
}

:deep(.flatpickr-day.selected) {
    background: #4361ee;
    border-color: #4361ee;
}

:deep(.flatpickr-day.today) {
    border-color: #4361ee;
}

:deep(.flatpickr-day:hover) {
    background: #f8f9fa;
}

:deep(.flatpickr-current-month) {
    padding: 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #4361ee;
}

:deep(.flatpickr-weekday) {
    color: #6c757d;
    font-weight: 600;
}

/* Custom styles for renewal warning icon */
.text-warning {
    color: #ffc107 !important;
    cursor: help;
}

.text-warning i {
    font-size: 16px;
}
</style>
